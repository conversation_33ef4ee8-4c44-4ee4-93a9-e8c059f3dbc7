# Production Database Management Guide

This guide provides practical methods for managing your Fly.io production database safely and efficiently.

## 🚀 Quick Start

### Prerequisites
- `flyctl` CLI installed and authenticated
- Access to your Fly.io applications (`reflex-chat-main`, `reflex-chat-api`, `reflex-chat-main-db`)
- DBeaver or similar database GUI tool (optional)

### Essential Scripts

```bash
# Database connection management
./scripts/db_connect.sh              # Start proxy and get connection details

# Production operations
./scripts/production_health.sh       # Check all services health
./scripts/backup_production_db.sh    # Create database backup
./scripts/production_deploy.sh       # Safe deployment workflow
./scripts/production_rollback.sh     # Emergency rollback

# Legacy database operations
./scripts/production_db_setup.sh status   # Check application status
./scripts/production_db_setup.sh inspect  # Inspect database contents
./scripts/production_db_setup.sh seed     # Seed database (first time)
```

## 🔗 Database Connection (DBeaver)

> **📖 For detailed DBeaver setup instructions, see [DBEAVER_CONNECTION_GUIDE.md](DBEAVER_CONNECTION_GUIDE.md)**

### Quick Connection Setup

**Step 1: Start Database Proxy**
```bash
# Use the connection management script
./scripts/db_connect.sh
```

**Step 2: Configure DBeaver**
- **Connection Type:** PostgreSQL
- **Host:** `localhost`
- **Port:** `5433`
- **Database:** `reflex_chat_main`
- **Username:** `reflex_chat_main`
- **Password:** `RefLex2024_Prod_DB!`

**Step 3: Test Connection**
```sql
SELECT version();
SELECT COUNT(*) FROM "user";
```

## 📊 Database Access Methods

### 1. GUI Access (Recommended)
```bash
# Start proxy and get connection details for DBeaver
./scripts/db_connect.sh
```

### 2. Command Line Access
```bash
# Quick inspection
./scripts/production_db_setup.sh inspect

# Direct connection (read-only)
./scripts/production_db_setup.sh connect

# Full access (use with caution)
./scripts/production_db_setup.sh connect-rw
```

### 3. Common Database Queries
```sql
-- List all tables
\dt

-- Count records
SELECT COUNT(*) FROM "user";
SELECT COUNT(*) FROM competency;
SELECT COUNT(*) FROM evaluation;

-- View sample data
SELECT id, name, email FROM "user" LIMIT 5;
SELECT name FROM role;
```

## 💾 Backup & Safety

### Creating Backups
```bash
# Automated backup with cleanup
./scripts/backup_production_db.sh

# Legacy backup method
./scripts/production_db_setup.sh backup
```

Backups are saved to `./backups/backup_production_YYYYMMDD_HHMMSS.sql`

### Before Making Changes
**Always create a backup first:**
```bash
./scripts/backup_production_db.sh
```

### Restoring from Backup (if needed)
```bash
# Start proxy
flyctl proxy 5433:5432 --app reflex-chat-main-db

# In another terminal, restore backup
PGPASSWORD="RefLex2024_Prod_DB!" psql -h localhost -p 5433 -U reflex_chat_main -d reflex_chat_main < backups/backup_file.sql
```

## 🌱 Database Seeding

### First-Time Setup
```bash
# Seed production database with initial data
./scripts/production_db_setup.sh seed
```

This creates:
- **Roles**: Analista, Consultor, Manager, Director, Socio
- **Competencies**: Performance and potential competencies from JSON files
- **Sample Users**: Including your user account
- **Projects**: Sample projects for testing
- **Role Mappings**: Links between roles and competencies

### What Gets Seeded
- ✅ 5 organizational roles
- ✅ ~50+ performance competencies
- ✅ ~30+ potential competencies  
- ✅ Role-competency mappings
- ✅ Sample users and projects
- ✅ Database tables and relationships

## 🔍 Monitoring & Logs

### Application Logs
```bash
# View logs for both applications
./scripts/production_db_setup.sh logs

# Or use flyctl directly
flyctl logs --app reflex-chat-main
flyctl logs --app reflex-chat-api
```

### Database Health
```bash
# Check application status
./scripts/production_db_setup.sh status

# Inspect database contents
./scripts/production_db_setup.sh inspect
```

## 🛠️ Common Tasks

### Check if Database is Seeded
```bash
./scripts/production_db_setup.sh inspect
```
Look for:
- Users > 0
- Roles = 5
- Competencies > 50

### Add a New User Manually
```bash
# Connect to database
./scripts/production_db_setup.sh connect-rw

# In psql:
INSERT INTO users (azure_id, name, email, role_id) 
VALUES ('your-azure-id', 'Your Name', '<EMAIL>', 1);
```

### View User Evaluations
```sql
-- Connect to database first
SELECT u.name, e.evaluation_type, e.status, e.created_at
FROM users u
JOIN evaluation e ON u.id = e.evaluatee_id
ORDER BY e.created_at DESC
LIMIT 10;
```

### Check Competency Data
```sql
-- View competencies by category
SELECT category, COUNT(*) as count
FROM competency
GROUP BY category;

-- View sample competencies
SELECT code, name, category, factor
FROM competency
LIMIT 10;
```

## ⚠️ Safety Guidelines

### DO's
- ✅ Always create backups before changes
- ✅ Use read-only connections for inspection
- ✅ Test changes in development first
- ✅ Use the provided scripts for common tasks

### DON'Ts
- ❌ Never run `DROP TABLE` or `DELETE FROM` without backup
- ❌ Don't modify production data directly without testing
- ❌ Don't share database credentials
- ❌ Don't run untested SQL scripts in production

## 🔄 Development vs Production Sync

### Keep Development Updated
```bash
# In development environment
docker-compose down
docker-compose up -d postgres

# Reset and reseed development database
python -c "from Reflex_Chat.database.db import reset_db_and_tables; reset_db_and_tables()"
python -c "from Reflex_Chat.database.seeds.seed_data import *; seed_roles(); seed_performance_competencies(); seed_potential_competencies(); seed_competency_role_map_from_json(); seed_users_and_projects()"
```

### Schema Changes
1. **Test in development first**
2. **Create production backup**
3. **Apply changes via migration scripts**
4. **Verify changes worked**

## 📞 Troubleshooting

### Connection Issues
```bash
# Check if applications are running
flyctl status --app reflex-chat-main
flyctl status --app reflex-chat-api

# Check logs for errors
flyctl logs --app reflex-chat-main --follow
```

### Database Connection Errors
```bash
# Verify database is attached
flyctl postgres list

# Check database status
flyctl status --app reflex-chat-main-db
```

### Seeding Failures
```bash
# Check logs during seeding
flyctl logs --app reflex-chat-main --follow

# Verify files exist
flyctl ssh console --app reflex-chat-main --command "ls -la /app/assets/"
```

## 🎯 Next Steps

1. **First-time setup**: Run `./scripts/production_db_setup.sh seed`
2. **Regular monitoring**: Use `./scripts/production_db_setup.sh inspect`
3. **Before changes**: Always run `./scripts/production_db_setup.sh backup`
4. **For investigation**: Use `./scripts/production_db_setup.sh connect`

This setup gives you full control over your production database while maintaining safety and simplicity.
