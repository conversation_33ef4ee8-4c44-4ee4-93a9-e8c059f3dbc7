name: Deploy to Fly.io

on:
  push:
    branches: [ main, production_FLY ]
  workflow_dispatch:

# Prevent concurrent deployments to avoid VM lease conflicts
concurrency:
  group: deploy-production
  cancel-in-progress: false

jobs:
  deploy:
    name: Deploy to Fly.io
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master



    - name: Verify configuration files
      run: |
        echo "Checking configuration files..."
        ls -la *.toml
        echo "Main app config:"
        head -5 fly.toml
        echo "FastAPI config:"
        head -5 fly-fastapi.toml

    - name: Deploy FastAPI Service
      run: |
        echo "Deploying FastAPI service..."
        flyctl deploy --config fly-fastapi.toml --remote-only --wait-timeout 600
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Wait for FastAPI deployment
      run: |
        echo "Waiting for FastAPI service to be ready..."
        sleep 60
        flyctl status --app reflex-chat-api
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Main Reflex App
      run: |
        echo "Deploying main Reflex application..."
        flyctl deploy --config fly.toml --remote-only --wait-timeout 1200
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Verify deployments
      run: |
        echo "Verifying FastAPI deployment..."
        flyctl status --app reflex-chat-api
        echo "Verifying main app deployment..."
        flyctl status --app reflex-chat-main
        echo "Testing API health endpoint..."
        curl -f https://reflex-chat-api.fly.dev/ || echo "API health check failed"
        echo "Testing main app health endpoint..."
        curl -f https://reflex-chat-main.fly.dev/health || echo "Main app health check failed"
        echo "Deployment verification completed"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
