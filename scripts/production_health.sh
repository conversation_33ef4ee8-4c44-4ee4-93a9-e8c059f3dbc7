#!/bin/bash
# Production health monitoring script
echo "=== Production Health Check $(date) ==="

# Check all services
echo "📱 Main App:"
flyctl status --app reflex-chat-main | grep -E "Status|Health" || echo "❌ Main app check failed"

echo "🔧 API:"
flyctl status --app reflex-chat-api | grep -E "Status|Health" || echo "❌ API check failed"

echo "🗄️ Database:"
flyctl status --app reflex-chat-main-db | grep -E "Status|Health" || echo "❌ Database check failed"

# Test endpoints
echo "🧪 Testing endpoints..."
curl -s -o /dev/null -w "Main App Health: %{http_code}\n" "https://reflex-chat-main.fly.dev/health"
curl -s -o /dev/null -w "API Health: %{http_code}\n" "https://reflex-chat-api.fly.dev/"

# Check dashboard data
echo "📊 Dashboard data test:"
curl -s "https://reflex-chat-api.fly.dev/api/users/1/user_role_project_summary" | jq '.user.name' 2>/dev/null || echo "❌ Dashboard data unavailable"

echo "=== Health check complete ==="
