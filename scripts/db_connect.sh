#!/bin/bash

# Database Connection Manager for Production PostgreSQL
set -e

echo "🔍 Database Connection Manager for Production PostgreSQL"
echo "======================================================="

# Check if proxy is already running
if lsof -i :5433 >/dev/null 2>&1; then
    echo "✅ Database proxy is already running on port 5433"
    PROXY_RUNNING=true
else
    echo "🚀 Starting database proxy..."
    echo "   Command: flyctl proxy 5433:5432 --app reflex-chat-main-db"
    echo "   Note: This will run in the background. Use 'kill \$PROXY_PID' to stop."
    echo ""
    
    # Start proxy in background
    flyctl proxy 5433:5432 --app reflex-chat-main-db &
    PROXY_PID=$!
    echo "   Proxy started with PID: $PROXY_PID"
    echo "   To stop proxy later: kill $PROXY_PID"
    sleep 3
    PROXY_RUNNING=false
fi

echo ""
echo "📋 DBeaver Connection Settings:"
echo "==============================="
echo "Connection Type: PostgreSQL"
echo "Host: localhost"
echo "Port: 5433"
echo "Database: reflex_chat_main"
echo "Username: reflex_chat_main"
echo "Password: RefLex2024_Prod_DB!"
echo ""

echo "🧪 Testing database connection..."
if command -v psql >/dev/null 2>&1; then
    echo "Running test query..."
    if PGPASSWORD="RefLex2024_Prod_DB!" psql -h localhost -p 5433 -U reflex_chat_main -d reflex_chat_main -c "SELECT COUNT(*) as total_users FROM \"user\";" 2>/dev/null; then
        echo "✅ Database connection test successful!"
    else
        echo "❌ Connection test failed. Check if proxy is running."
    fi
else
    echo "⚠️  psql not found. Install PostgreSQL client to test connection."
fi

echo ""
echo "📖 DBeaver Setup Instructions:"
echo "1. Open DBeaver"
echo "2. Create new connection → PostgreSQL"
echo "3. Use the connection settings above"
echo "4. Click 'Test Connection' to verify"
echo "5. Save and connect"
echo ""

echo "🔧 Useful queries to try in DBeaver:"
echo "   -- View all users"
echo "   SELECT * FROM \"user\" LIMIT 10;"
echo ""
echo "   -- View evaluations"
echo "   SELECT * FROM evaluation LIMIT 10;"
echo ""
echo "   -- View competencies"
echo "   SELECT * FROM competency LIMIT 10;"
echo ""
echo "   -- View evaluation answers"
echo "   SELECT * FROM evaluationanswer LIMIT 10;"
echo ""

if [ "$PROXY_RUNNING" = false ] && [ ! -z "$PROXY_PID" ]; then
    echo "⚠️  Remember to stop the proxy when done:"
    echo "   kill $PROXY_PID"
fi

echo ""
echo "🎯 Database is ready for DBeaver connection!"
