#!/bin/bash
# Production database backup script

# Create backups directory if it doesn't exist
mkdir -p backups

# Create timestamped backup
BACKUP_FILE="backup_production_$(date +%Y%m%d_%H%M%S).sql"
echo "💾 Creating production database backup..."

# Create backup
flyctl postgres connect --app reflex-chat-main-db --command "pg_dump reflex_chat_main" > "backups/$BACKUP_FILE"

# Verify backup was created
if [ -f "backups/$BACKUP_FILE" ]; then
    BACKUP_SIZE=$(du -h "backups/$BACKUP_FILE" | cut -f1)
    echo "✅ Backup created successfully: backups/$BACKUP_FILE ($BACKUP_SIZE)"
    
    # Keep only last 10 backups
    cd backups
    ls -t backup_production_*.sql | tail -n +11 | xargs -r rm
    echo "🧹 Cleaned up old backups (keeping last 10)"
    
    # List current backups
    echo "📋 Current backups:"
    ls -lah backup_production_*.sql 2>/dev/null || echo "No backups found"
else
    echo "❌ Backup failed!"
    exit 1
fi
