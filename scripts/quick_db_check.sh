#!/bin/bash

# Quick Database Health Check Script
# Provides a fast overview of your production database status

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

MAIN_APP="reflex-chat-main"
API_APP="reflex-chat-api"

echo -e "${BLUE}🔍 Quick Database Health Check${NC}"
echo "================================"

# Check if apps are running
echo -e "${YELLOW}📱 Checking application status...${NC}"
if flyctl status --app "$MAIN_APP" &> /dev/null; then
    echo -e "${GREEN}✅ Main app ($MAIN_APP) is running${NC}"
else
    echo -e "${RED}❌ Main app ($MAIN_APP) is not running${NC}"
    exit 1
fi

if flyctl status --app "$API_APP" &> /dev/null; then
    echo -e "${GREEN}✅ API app ($API_APP) is running${NC}"
else
    echo -e "${RED}❌ API app ($API_APP) is not running${NC}"
fi

echo ""

# Quick database check
echo -e "${YELLOW}🗃️  Checking database contents...${NC}"
flyctl ssh console --app "$MAIN_APP" --command "cd /app && python -c \"
try:
    from Reflex_Chat.database.db import get_session
    from Reflex_Chat.database.models import *
    from sqlmodel import select
    
    with get_session() as session:
        users = session.exec(select(User)).all()
        roles = session.exec(select(Role)).all()
        competencies = session.exec(select(Competency)).all()
        evaluations = session.exec(select(Evaluation)).all()
        
        print('📊 Database Status:')
        print(f'👥 Users: {len(users)}')
        print(f'🎭 Roles: {len(roles)}')
        print(f'🎯 Competencies: {len(competencies)}')
        print(f'📊 Evaluations: {len(evaluations)}')
        
        # Check if database is properly seeded
        if len(roles) >= 5 and len(competencies) >= 50:
            print('✅ Database appears to be properly seeded')
        elif len(roles) == 0 and len(competencies) == 0:
            print('⚠️  Database appears to be empty - needs seeding')
        else:
            print('⚠️  Database partially seeded - may need attention')
            
except Exception as e:
    print(f'❌ Database connection error: {str(e)}')
    print('💡 Database may need to be created or seeded')
\""

echo ""
echo -e "${BLUE}💡 Quick Actions:${NC}"
echo "  Seed database:    ./scripts/production_db_setup.sh seed"
echo "  Full inspection:  ./scripts/production_db_setup.sh inspect"
echo "  Create backup:    ./scripts/production_db_setup.sh backup"
echo "  Connect to DB:    ./scripts/production_db_setup.sh connect"
