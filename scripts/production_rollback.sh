#!/bin/bash
# Emergency rollback script
echo "🚨 Starting emergency rollback..."

# Rollback API
echo "⏪ Rolling back API..."
flyctl releases rollback --app reflex-chat-api

# Wait for API to stabilize
echo "⏳ Waiting for API rollback..."
sleep 30

# Rollback main app
echo "⏪ Rolling back main app..."
flyctl releases rollback --app reflex-chat-main

# Verify rollback
echo "🔍 Verifying rollback..."
sleep 30
./scripts/production_health.sh

echo "✅ Rollback complete!"
