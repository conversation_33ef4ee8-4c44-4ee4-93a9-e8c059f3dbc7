#!/usr/bin/env python3
"""
Simple database management script for Fly.io production environment.
Provides safe, practical database operations for the Reflex Chat application.
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from typing import Optional, List, Dict, Any

class FlyDBManager:
    """Simple database management for Fly.io deployed applications."""
    
    def __init__(self, main_app: str = "reflex-chat-main", api_app: str = "reflex-chat-api"):
        self.main_app = main_app
        self.api_app = api_app
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def run_fly_command(self, command: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run a flyctl command safely."""
        try:
            result = subprocess.run(
                command, 
                capture_output=capture_output, 
                text=True, 
                check=True
            )
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed: {' '.join(command)}")
            print(f"Error: {e.stderr if e.stderr else e.stdout}")
            sys.exit(1)
    
    def check_apps_status(self):
        """Check the status of both applications."""
        print("🔍 Checking application status...")
        
        for app in [self.main_app, self.api_app]:
            print(f"\n📱 {app}:")
            try:
                result = self.run_fly_command(["flyctl", "status", "--app", app])
                print("✅ Running")
            except:
                print("❌ Not running or not accessible")
    
    def connect_to_database(self, read_only: bool = True):
        """Connect to the production database via proxy."""
        print("🔗 Connecting to production database...")
        
        if read_only:
            print("⚠️  Opening READ-ONLY connection for safety")
            print("💡 Use psql commands like \\dt to list tables, \\d table_name to describe tables")
        else:
            print("⚠️  Opening WRITE connection - BE CAREFUL!")
        
        # Open database proxy connection
        proxy_cmd = ["flyctl", "proxy", "5433:5432", "--app", self.main_app]
        print(f"🚀 Starting proxy: {' '.join(proxy_cmd)}")
        print("📝 In another terminal, connect with:")
        print("   psql postgresql://postgres:password@localhost:5433/database_name")
        print("🛑 Press Ctrl+C to stop the proxy")
        
        try:
            subprocess.run(proxy_cmd)
        except KeyboardInterrupt:
            print("\n✅ Database proxy stopped")
    
    def backup_database(self):
        """Create a backup of the production database."""
        backup_file = f"backup_production_{self.timestamp}.sql"
        print(f"💾 Creating database backup: {backup_file}")
        
        # Use flyctl to create a backup
        cmd = [
            "flyctl", "ssh", "console", "--app", self.main_app, 
            "--command", f"pg_dump $DATABASE_URL > /tmp/{backup_file}"
        ]
        
        print("🔄 Creating backup (this may take a few minutes)...")
        self.run_fly_command(cmd, capture_output=False)
        
        # Download the backup
        download_cmd = [
            "flyctl", "ssh", "sftp", "get", f"/tmp/{backup_file}", 
            f"./backups/{backup_file}", "--app", self.main_app
        ]
        
        os.makedirs("backups", exist_ok=True)
        self.run_fly_command(download_cmd, capture_output=False)
        print(f"✅ Backup saved to ./backups/{backup_file}")
    
    def seed_production_database(self, confirm: bool = False):
        """Seed the production database with initial data."""
        if not confirm:
            print("⚠️  This will seed the production database with initial data.")
            print("🔍 This includes roles, competencies, and sample users.")
            response = input("Are you sure you want to proceed? (yes/no): ")
            if response.lower() != 'yes':
                print("❌ Seeding cancelled")
                return
        
        print("🌱 Seeding production database...")
        
        # Create the seeding command
        seed_commands = [
            "from Reflex_Chat.database.db import create_db_and_tables",
            "create_db_and_tables()",
            "from Reflex_Chat.database.seeds.seed_data import *",
            "seed_roles()",
            "seed_performance_competencies()",
            "seed_potential_competencies()",
            "seed_competency_role_map_from_json()",
            "seed_users_and_projects()",
            "print('✅ Database seeding completed successfully')"
        ]
        
        python_cmd = "; ".join(seed_commands)
        
        cmd = [
            "flyctl", "ssh", "console", "--app", self.main_app,
            "--command", f"cd /app && python -c \"{python_cmd}\""
        ]
        
        print("🔄 Running seeding commands...")
        self.run_fly_command(cmd, capture_output=False)
        print("✅ Production database seeded successfully")
    
    def inspect_database(self):
        """Show basic database information."""
        print("🔍 Inspecting production database...")
        
        inspect_commands = [
            "from Reflex_Chat.database.db import get_session",
            "from Reflex_Chat.database.models import *",
            "from sqlmodel import select",
            "with get_session() as session:",
            "    users = session.exec(select(User)).all()",
            "    roles = session.exec(select(Role)).all()",
            "    competencies = session.exec(select(Competency)).all()",
            "    evaluations = session.exec(select(Evaluation)).all()",
            "    print(f'👥 Users: {len(users)}')",
            "    print(f'🎭 Roles: {len(roles)}')",
            "    print(f'🎯 Competencies: {len(competencies)}')",
            "    print(f'📊 Evaluations: {len(evaluations)}')",
            "    if users:",
            "        print('\\n👥 Users:')",
            "        for u in users[:5]:",
            "            print(f'  - {u.name} ({u.email})')",
            "    if roles:",
            "        print('\\n🎭 Roles:')",
            "        for r in roles:",
            "            print(f'  - {r.name}')"
        ]
        
        python_cmd = "; ".join(inspect_commands)
        
        cmd = [
            "flyctl", "ssh", "console", "--app", self.main_app,
            "--command", f"cd /app && python -c \"{python_cmd}\""
        ]
        
        self.run_fly_command(cmd, capture_output=False)
    
    def show_logs(self, app: Optional[str] = None, lines: int = 100):
        """Show application logs."""
        apps = [app] if app else [self.main_app, self.api_app]
        
        for app_name in apps:
            print(f"\n📋 Logs for {app_name} (last {lines} lines):")
            cmd = ["flyctl", "logs", "--app", app_name, "-n", str(lines)]
            self.run_fly_command(cmd, capture_output=False)


def main():
    """Main CLI interface for database management."""
    if len(sys.argv) < 2:
        print("🗃️  Fly.io Database Management Tool")
        print("\nUsage: python scripts/db_management.py <command>")
        print("\nCommands:")
        print("  status      - Check application status")
        print("  connect     - Connect to database (read-only)")
        print("  connect-rw  - Connect to database (read-write)")
        print("  backup      - Create database backup")
        print("  seed        - Seed database with initial data")
        print("  inspect     - Show database statistics")
        print("  logs        - Show application logs")
        print("  logs-main   - Show main app logs only")
        print("  logs-api    - Show API app logs only")
        sys.exit(1)
    
    command = sys.argv[1]
    manager = FlyDBManager()
    
    if command == "status":
        manager.check_apps_status()
    elif command == "connect":
        manager.connect_to_database(read_only=True)
    elif command == "connect-rw":
        manager.connect_to_database(read_only=False)
    elif command == "backup":
        manager.backup_database()
    elif command == "seed":
        manager.seed_production_database()
    elif command == "inspect":
        manager.inspect_database()
    elif command == "logs":
        manager.show_logs()
    elif command == "logs-main":
        manager.show_logs(app=manager.main_app)
    elif command == "logs-api":
        manager.show_logs(app=manager.api_app)
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
