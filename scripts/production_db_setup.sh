#!/bin/bash

# Production Database Setup Script for Fly.io
# This script helps you set up and manage your production database safely

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAIN_APP="reflex-chat-main"
API_APP="reflex-chat-api"
DB_APP="${MAIN_APP}-db"
REDIS_APP="${MAIN_APP}-redis"

echo -e "${BLUE}🗃️  Reflex Chat - Production Database Setup${NC}"
echo "=============================================="

# Function to check if flyctl is installed
check_flyctl() {
    if ! command -v flyctl &> /dev/null; then
        echo -e "${RED}❌ flyctl is not installed. Please install it first:${NC}"
        echo "   curl -L https://fly.io/install.sh | sh"
        exit 1
    fi
    echo -e "${GREEN}✅ flyctl is installed${NC}"
}

# Function to check if user is logged in to Fly.io
check_fly_auth() {
    if ! flyctl auth whoami &> /dev/null; then
        echo -e "${RED}❌ Not logged in to Fly.io. Please run:${NC}"
        echo "   flyctl auth login"
        exit 1
    fi
    echo -e "${GREEN}✅ Logged in to Fly.io${NC}"
}

# Function to check application status
check_app_status() {
    local app=$1
    echo -e "${BLUE}📱 Checking $app status...${NC}"
    
    if flyctl status --app "$app" &> /dev/null; then
        echo -e "${GREEN}✅ $app is running${NC}"
        return 0
    else
        echo -e "${RED}❌ $app is not running or not accessible${NC}"
        return 1
    fi
}

# Function to create database backup
create_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="backup_production_${timestamp}.sql"
    
    echo -e "${YELLOW}💾 Creating database backup...${NC}"
    
    # Create backups directory if it doesn't exist
    mkdir -p backups
    
    # Create backup using pg_dump
    echo -e "${BLUE}🔄 Running pg_dump on production database...${NC}"
    flyctl ssh console --app "$MAIN_APP" --command "pg_dump \$DATABASE_URL" > "backups/$backup_file"
    
    if [ -f "backups/$backup_file" ]; then
        echo -e "${GREEN}✅ Backup created: backups/$backup_file${NC}"
        echo -e "${BLUE}💡 Backup size: $(du -h "backups/$backup_file" | cut -f1)${NC}"
    else
        echo -e "${RED}❌ Backup failed${NC}"
        exit 1
    fi
}

# Function to seed the database
seed_database() {
    echo -e "${YELLOW}⚠️  This will seed the production database with initial data.${NC}"
    echo -e "${YELLOW}   This includes roles, competencies, and sample users.${NC}"
    echo ""
    read -p "Are you sure you want to proceed? (yes/no): " -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        echo -e "${RED}❌ Seeding cancelled${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🌱 Seeding production database...${NC}"
    
    # Create tables first
    echo -e "${BLUE}📋 Creating database tables...${NC}"
    flyctl ssh console --app "$MAIN_APP" --command "cd /app && python -c \"from Reflex_Chat.database.db import create_db_and_tables; create_db_and_tables(); print('✅ Tables created')\""
    
    # Seed data
    echo -e "${BLUE}🌱 Seeding initial data...${NC}"
    flyctl ssh console --app "$MAIN_APP" --command "cd /app && python -c \"
from Reflex_Chat.database.seeds.seed_data import *
print('🎭 Seeding roles...')
seed_roles()
print('🎯 Seeding performance competencies...')
seed_performance_competencies()
print('🚀 Seeding potential competencies...')
seed_potential_competencies()
print('🗺️  Seeding competency role mappings...')
seed_competency_role_map_from_json()
print('👥 Seeding users and projects...')
seed_users_and_projects()
print('✅ Database seeding completed successfully')
\""
    
    echo -e "${GREEN}✅ Production database seeded successfully${NC}"
}

# Function to inspect database
inspect_database() {
    echo -e "${BLUE}🔍 Inspecting production database...${NC}"
    
    flyctl ssh console --app "$MAIN_APP" --command "cd /app && python -c \"
from Reflex_Chat.database.db import get_session
from Reflex_Chat.database.models import *
from sqlmodel import select

with get_session() as session:
    users = session.exec(select(User)).all()
    roles = session.exec(select(Role)).all()
    competencies = session.exec(select(Competency)).all()
    evaluations = session.exec(select(Evaluation)).all()
    projects = session.exec(select(Project)).all()
    
    print('📊 Database Statistics:')
    print(f'👥 Users: {len(users)}')
    print(f'🎭 Roles: {len(roles)}')
    print(f'🎯 Competencies: {len(competencies)}')
    print(f'📊 Evaluations: {len(evaluations)}')
    print(f'📁 Projects: {len(projects)}')
    
    if users:
        print('\\n👥 Users:')
        for u in users[:10]:  # Show first 10 users
            role_name = 'No role'
            if u.role_id:
                role = session.get(Role, u.role_id)
                if role:
                    role_name = role.name
            print(f'  - {u.name} ({u.email}) - {role_name}')
    
    if roles:
        print('\\n🎭 Roles:')
        for r in roles:
            user_count = len([u for u in users if u.role_id == r.id])
            print(f'  - {r.name} ({user_count} users)')
\""
}

# Function to connect to database
connect_database() {
    local mode=$1
    
    if [ "$mode" = "readonly" ]; then
        echo -e "${BLUE}🔗 Opening READ-ONLY database connection...${NC}"
        echo -e "${YELLOW}⚠️  This is a read-only connection for safety${NC}"
    else
        echo -e "${BLUE}🔗 Opening READ-WRITE database connection...${NC}"
        echo -e "${RED}⚠️  BE CAREFUL - This allows database modifications!${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}💡 Database connection tips:${NC}"
    echo "   \\dt          - List all tables"
    echo "   \\d table     - Describe table structure"
    echo "   SELECT COUNT(*) FROM users;  - Count users"
    echo "   \\q           - Quit"
    echo ""
    echo -e "${YELLOW}🚀 Starting database proxy on localhost:5433...${NC}"
    echo -e "${YELLOW}🛑 Press Ctrl+C to stop the proxy${NC}"
    echo ""
    
    # Start the proxy
    flyctl proxy 5433:5432 --app "$MAIN_APP"
}

# Main menu
show_menu() {
    echo ""
    echo -e "${BLUE}Available commands:${NC}"
    echo "  1. status    - Check application status"
    echo "  2. backup    - Create database backup"
    echo "  3. seed      - Seed database with initial data"
    echo "  4. inspect   - Show database statistics"
    echo "  5. connect   - Connect to database (read-only)"
    echo "  6. connect-rw - Connect to database (read-write)"
    echo "  7. logs      - Show application logs"
    echo ""
}

# Parse command line arguments
case "${1:-menu}" in
    "status")
        check_flyctl
        check_fly_auth
        check_app_status "$MAIN_APP"
        check_app_status "$API_APP"
        ;;
    "backup")
        check_flyctl
        check_fly_auth
        create_backup
        ;;
    "seed")
        check_flyctl
        check_fly_auth
        seed_database
        ;;
    "inspect")
        check_flyctl
        check_fly_auth
        inspect_database
        ;;
    "connect")
        check_flyctl
        check_fly_auth
        connect_database "readonly"
        ;;
    "connect-rw")
        check_flyctl
        check_fly_auth
        connect_database "readwrite"
        ;;
    "logs")
        check_flyctl
        check_fly_auth
        echo -e "${BLUE}📋 Main App Logs:${NC}"
        flyctl logs --app "$MAIN_APP" -n 50
        echo ""
        echo -e "${BLUE}📋 API App Logs:${NC}"
        flyctl logs --app "$API_APP" -n 50
        ;;
    "menu"|*)
        check_flyctl
        check_fly_auth
        show_menu
        echo -e "${YELLOW}Usage: $0 <command>${NC}"
        echo -e "${YELLOW}Example: $0 status${NC}"
        ;;
esac
