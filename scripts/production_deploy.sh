#!/bin/bash
# Safe production deployment script
set -e

echo "🚀 Starting production deployment..."

# Pre-deployment checks
echo "🔍 Pre-deployment checks..."
./scripts/production_health.sh

# Create backups directory if it doesn't exist
mkdir -p backups

# Backup database
echo "💾 Creating database backup..."
BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
flyctl postgres connect --app reflex-chat-main-db --command "pg_dump reflex_chat_main" > "backups/$BACKUP_FILE"
echo "✅ Backup created: backups/$BACKUP_FILE"

# Deploy API first
echo "🔧 Deploying FastAPI backend..."
flyctl deploy --app reflex-chat-api --config fly-fastapi.toml

# Wait and verify API
echo "⏳ Waiting for API to stabilize..."
sleep 30
curl -f "https://reflex-chat-api.fly.dev/" || { echo "❌ API deployment failed"; exit 1; }

# Deploy frontend
echo "📱 Deploying Reflex frontend..."
flyctl deploy --app reflex-chat-main

# Final verification
echo "🔍 Post-deployment verification..."
sleep 30
./scripts/production_health.sh

echo "✅ Deployment complete!"
