# DBeaver Connection Guide for Fly.io Production Database

Complete step-by-step guide for connecting DBeaver to your production PostgreSQL database on Fly.io.

## 🎯 Quick Start (Recommended Method)

### Step 1: Create Secure Tunnel
```bash
# Open terminal and create secure tunnel
flyctl proxy 5433:5432 --app reflex-chat-main-db

# Keep this terminal open - you'll see:
# "Proxying local port 5433 to remote [reflex-chat-main-db.internal]:5432"
```

### Step 2: Use Connection Script (Recommended)
```bash
# In a NEW terminal, use the connection management script
./scripts/db_connect.sh

# This will:
# - Start the proxy if not running
# - Display connection details
# - Test the connection
```

### Step 3: Configure DBeaver
1. **Open DBeaver**
2. **New Database Connection** → **PostgreSQL**
3. **Main Tab Settings:**
   - **Server Host:** `localhost`
   - **Port:** `5433` (the proxy port)
   - **Database:** `reflex_chat_main`
   - **Username:** `reflex_chat_main`
   - **Password:** `RefLex2024_Prod_DB!`
   - **Show all databases:** ✅ (checked)

4. **Test Connection** → Should show "Connected"
5. **Finish** to save the connection

### Step 4: Verify Connection
```sql
-- Test these queries in DBeaver
SELECT version();
SELECT current_database();
SELECT COUNT(*) FROM "user";
SELECT COUNT(*) FROM competency;
SELECT COUNT(*) FROM evaluation;
```

## 🔧 Alternative Connection Methods

### Method 2: Direct Connection with SSH Tunnel

**Step 1: Configure SSH Tunnel in DBeaver**
1. **New Connection** → **PostgreSQL**
2. **SSH Tab:**
   - **Use SSH Tunnel:** ✅ (checked)
   - **Host/IP:** `reflex-chat-api.fly.dev`
   - **Port:** `22`
   - **User Name:** `root`
   - **Authentication Method:** `Public Key`
   - **Private Key:** [Your SSH key path]

3. **Main Tab:**
   - **Server Host:** `reflex-chat-main-db.flycast`
   - **Port:** `5432`
   - **Database:** `reflex_chat_main`
   - **Username:** `reflex_chat_main`
   - **Password:** `RefLex2024_Prod_DB!`

### Method 3: Using flyctl as Proxy

**Step 1: Create Connection Script**
```bash
#!/bin/bash
# connect_dbeaver.sh
echo "🔗 Starting DBeaver connection proxy..."
echo "📝 Connection details:"
echo "  Host: localhost"
echo "  Port: 5433"
echo "  Database: reflex_chat_main"
echo "  Username: reflex_chat_main"
echo ""
echo "🚀 Starting proxy (keep this terminal open)..."
flyctl proxy 5433:5432 --app reflex-chat-main-db
```

**Step 2: Run Script and Connect**
```bash
chmod +x connect_dbeaver.sh
./connect_dbeaver.sh
```

## 🛠️ Troubleshooting

### Issue 1: "Connection refused"
**Symptoms:** Cannot connect to localhost:5433
**Solutions:**
```bash
# Check if proxy is running
ps aux | grep flyctl

# Check if port 5433 is available
lsof -i :5433

# Try different port
flyctl proxy 5434:5432 --app reflex-chat-main-db
# Then use port 5434 in DBeaver
```

### Issue 2: "Authentication failed"
**Symptoms:** Wrong username/password error
**Solutions:**
```bash
# Verify current credentials (should be RefLex2024_Prod_DB!)
./scripts/db_connect.sh

# Verify database users
flyctl postgres users list --app reflex-chat-main-db

# Test connection manually
PGPASSWORD="RefLex2024_Prod_DB!" psql -h localhost -p 5433 -U reflex_chat_main -d reflex_chat_main -c "SELECT 1;"
```

### Issue 3: "Database does not exist"
**Symptoms:** Database not found error
**Solutions:**
```bash
# List available databases
flyctl postgres db list --app reflex-chat-main-db

# Create database if missing
flyctl postgres connect --app reflex-chat-main-db --command "CREATE DATABASE reflex_chat_main;"
```

### Issue 4: "Connection timeout"
**Symptoms:** Connection times out
**Solutions:**
```bash
# Check database status
flyctl status --app reflex-chat-main-db

# Check if database is sleeping
flyctl machine list --app reflex-chat-main-db

# Wake up database
flyctl machine start [machine-id] --app reflex-chat-main-db

# Increase timeout in DBeaver:
# Connection Properties → Driver Properties → socketTimeout = 60
```

### Issue 5: "SSL connection error"
**Symptoms:** SSL/TLS handshake failures
**Solutions:**
1. **For Proxy Connections:**
   - Set SSL Mode to "disable" or "prefer"
   - Uncheck "Use SSL"

2. **For Direct Connections:**
   - Set SSL Mode to "require"
   - Check "Use SSL"

## 🔐 Security Best Practices

### Connection Security
- ✅ Always use the proxy method for external access
- ✅ Close DBeaver connections when not in use
- ✅ Use read-only queries for inspection
- ✅ Never share connection credentials

### Data Safety
- ✅ Create backups before any modifications
- ✅ Test queries on development first
- ✅ Use transactions for multiple operations
- ✅ Avoid running destructive queries

### Access Control
```sql
-- Create read-only user for safer access
CREATE USER readonly_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE reflex_chat_main TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
```

## 📊 Useful Queries for Production

### Database Overview
```sql
-- Database size and statistics
SELECT 
    pg_size_pretty(pg_database_size(current_database())) as database_size,
    (SELECT count(*) FROM "user") as users,
    (SELECT count(*) FROM competency) as competencies,
    (SELECT count(*) FROM evaluation) as evaluations,
    (SELECT count(*) FROM answer) as answers;

-- Table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### User Data Analysis
```sql
-- User overview
SELECT 
    u.id,
    u.name,
    u.email,
    r.name as role,
    u.last_login,
    COUNT(e.id) as evaluation_count
FROM "user" u
LEFT JOIN role r ON u.role_id = r.id
LEFT JOIN evaluation e ON u.id = e.evaluatee_id
GROUP BY u.id, u.name, u.email, r.name, u.last_login
ORDER BY u.last_login DESC;

-- Recent evaluations
SELECT 
    u.name as user_name,
    e.evaluation_type,
    e.status,
    e.created_at,
    COUNT(a.id) as answer_count
FROM evaluation e
JOIN "user" u ON e.evaluatee_id = u.id
LEFT JOIN answer a ON e.id = a.evaluation_id
GROUP BY u.name, e.evaluation_type, e.status, e.created_at
ORDER BY e.created_at DESC
LIMIT 10;
```

### Performance Monitoring
```sql
-- Active connections
SELECT 
    count(*) as active_connections,
    state,
    application_name
FROM pg_stat_activity 
WHERE state IS NOT NULL
GROUP BY state, application_name;

-- Slow queries (if pg_stat_statements is enabled)
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;
```

## 🔄 Connection Maintenance

### Daily Checks
```bash
# Test connection
./connect_dbeaver.sh &
sleep 5
curl -s "localhost:5433" && echo "✅ Proxy working" || echo "❌ Proxy failed"
```

### Weekly Maintenance
```sql
-- Update table statistics
ANALYZE;

-- Check for unused indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE idx_scan = 0;
```

## 📱 Alternative GUI Tools

### pgAdmin Configuration
```
Host: localhost (with proxy)
Port: 5433
Database: reflex_chat_main
Username: reflex_chat_main
Password: RefLex2024_Prod_DB!
SSL Mode: Prefer
```

### TablePlus Configuration
```
Type: PostgreSQL
Host: localhost
Port: 5433
User: reflex_chat_main
Password: RefLex2024_Prod_DB!
Database: reflex_chat_main
```

### Postico Configuration
```
Host: localhost
Port: 5433
User: reflex_chat_main
Password: RefLex2024_Prod_DB!
Database: reflex_chat_main
SSL: Off (for proxy connections)
```

## 🚀 Quick Reference

**Start Connection:**
```bash
flyctl proxy 5433:5432 --app reflex-chat-main-db
```

**DBeaver Settings:**
- Host: `localhost`
- Port: `5433`
- Database: `reflex_chat_main`
- Username: `reflex_chat_main`
- Password: `RefLex2024_Prod_DB!`

**Test Connection:**
```sql
SELECT 'Connection successful!' as status, current_database(), current_user;
```

This guide provides everything needed to reliably connect DBeaver to your production database with proper security and troubleshooting support.
