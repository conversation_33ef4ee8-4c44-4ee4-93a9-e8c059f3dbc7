# Fly.io configuration for FastAPI service
app = "reflex-chat-api"
primary_region = "iad"

[build]
  dockerfile = "Reflex_Chat/api/Dockerfile.production"
  context = "."

[env]
  PYTHONUNBUFFERED = "1"
  PYTHONPATH = "/app"

[http_service]
  internal_port = 8001
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "90s"
    interval = "60s"
    method = "GET"
    timeout = "20s"
    path = "/"

[http_service.concurrency]
  type = "connections"
  hard_limit = 500
  soft_limit = 250

[[vm]]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1
