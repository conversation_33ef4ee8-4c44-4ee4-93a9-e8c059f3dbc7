#AUTHENTICATION CONFIG
TENANT_ID=your_tenant_id
CLIENT_ID=your_client_id
REDIRECT_URI=http://localhost:3000/auth/callback
BASE_URL=http://localhost:3000
CLIENT_SECRET=your_client_secret

#SEARCH CONFIG
SEARCH_SERVICE_NAME=your_search_service_name
SEARCH_API_KEY=your_search_api_key
BLOB_CONNECTION_STRING=your_blob_connection_string
BLOB_CONTAINER_NAME=your_container_name
INDEX_NAME=your_index_name

#OPENAI CONFIG
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4-turbo

#POSTGRES CONFIG
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=postgres

# DATABASE (same as Postgres)
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}

# REDIS
REDIS_URL=redis://redis:6379

# REFLEX
FRONTEND_PORT=3000
BACKEND_PORT=8000
PYTHONUNBUFFERED=1

#API
API_URL=http://fastapi:8001
FASTAPI_URL=http://fastapi:8001
WEBSOCKET_URL=ws://localhost:8000

#for seeding data
USER_AZURE_ID=your_user_azure_id

