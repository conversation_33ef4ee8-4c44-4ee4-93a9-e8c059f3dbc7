# Production Operations - Quick Reference

This document provides a consolidated overview of all production operations documentation and scripts.

## 📚 Documentation Structure

### Core Guides
- **[DATABASE_MANAGEMENT.md](DATABASE_MANAGEMENT.md)** - Database operations, backup, and seeding
- **[DBEAVER_CONNECTION_GUIDE.md](DBEAVER_CONNECTION_GUIDE.md)** - Complete DBeaver setup guide
- **[PRODUCTION_OPERATIONS.md](PRODUCTION_OPERATIONS.md)** - Deployment, monitoring, and troubleshooting

### GitHub Actions
- **[.github/workflows/deploy.yml](.github/workflows/deploy.yml)** - Automated deployment pipeline

## 🛠️ Essential Scripts

### Database Operations
```bash
./scripts/db_connect.sh              # Start proxy and connect to database
./scripts/backup_production_db.sh    # Create database backup with cleanup
```

### Production Management
```bash
./scripts/production_health.sh       # Check all services health
./scripts/production_deploy.sh       # Safe deployment workflow
./scripts/production_rollback.sh     # Emergency rollback procedure
```

### Legacy Scripts
```bash
./scripts/production_db_setup.sh     # Legacy database operations
```

## 🚀 Quick Start Guide

### Daily Operations
```bash
# 1. Check system health
./scripts/production_health.sh

# 2. Check for errors
flyctl logs --app reflex-chat-api --no-tail | grep -i error | tail -5
flyctl logs --app reflex-chat-main --no-tail | grep -i error | tail -5
```

### Database Access
```bash
# 1. Connect DBeaver to production database
./scripts/db_connect.sh

# 2. Use these connection settings in DBeaver:
# Host: localhost
# Port: 5433
# Database: reflex_chat_main
# Username: reflex_chat_main
# Password: RefLex2024_Prod_DB!
```

### Deployment
```bash
# 1. Automated deployment (recommended)
./scripts/production_deploy.sh

# 2. Manual deployment
./scripts/backup_production_db.sh
flyctl deploy --app reflex-chat-api --config fly-fastapi.toml
flyctl deploy --app reflex-chat-main
./scripts/production_health.sh
```

### Emergency Procedures
```bash
# 1. Emergency rollback
./scripts/production_rollback.sh

# 2. Database backup before changes
./scripts/backup_production_db.sh
```

## 🔐 Production Credentials

### Database Connection
- **Host:** `localhost` (with proxy) or `reflex-chat-main-db.flycast` (direct)
- **Port:** `5433` (proxy) or `5432` (direct)
- **Database:** `reflex_chat_main`
- **Username:** `reflex_chat_main`
- **Password:** `RefLex2024_Prod_DB!`

### Fly.io Applications
- **Main App:** `reflex-chat-main` (Frontend)
- **API App:** `reflex-chat-api` (Backend)
- **Database:** `reflex-chat-main-db` (PostgreSQL)

## 📊 Monitoring Endpoints

### Health Checks
- **Main App:** https://reflex-chat-main.fly.dev/health
- **API:** https://reflex-chat-api.fly.dev/
- **Dashboard Data:** https://reflex-chat-api.fly.dev/api/users/1/user_role_project_summary

### Status Commands
```bash
flyctl status --app reflex-chat-main
flyctl status --app reflex-chat-api
flyctl status --app reflex-chat-main-db
```

## 🔄 GitHub Actions Deployment

### Automatic Deployment
- **Triggers:** Push to `main` or `production_FLY` branches
- **Workflow:** Deploy API first, then frontend
- **Includes:** Health checks and verification

### Manual Deployment
```bash
# Trigger deployment manually
git push origin production_FLY
```

## ⚠️ Important Notes

### Security
- Database password was reset to `RefLex2024_Prod_DB!` on 2025-01-17
- All documentation and scripts updated with new credentials
- Old DATABASE_URL references removed

### Backup Policy
- Automated backups keep last 10 files
- Backups stored in `./backups/` directory
- Always backup before major changes

### Script Dependencies
- All scripts require `flyctl` CLI installed and authenticated
- Database scripts require PostgreSQL client (`psql`) for testing
- Health scripts require `curl` and optionally `jq`

## 🆘 Emergency Contacts

### Quick Recovery Steps
1. **Service Down:** `./scripts/production_health.sh` to diagnose
2. **Deployment Failed:** `./scripts/production_rollback.sh`
3. **Database Issues:** Check logs and verify connection with `./scripts/db_connect.sh`
4. **Complete Outage:** Check Fly.io status page and restart services

### Log Analysis
```bash
# Recent errors
flyctl logs --app reflex-chat-api --no-tail | grep -i error | tail -20

# Performance issues
flyctl logs --app reflex-chat-main --no-tail | grep -E "slow|timeout|502|503" | tail -10

# Authentication issues
flyctl logs --app reflex-chat-main --no-tail | grep -i "auth\|login\|401\|403" | tail -10
```

This consolidated guide provides everything needed for efficient production operations management.
