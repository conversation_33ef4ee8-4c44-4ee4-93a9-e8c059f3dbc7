# Production Operations Guide

Comprehensive guide for managing your Fly.io production environment including deployment, monitoring, scaling, and troubleshooting.

## 🚀 Application Deployment & Management

### 1. Deployment Workflow

**Deploy FastAPI Backend:**
```bash
# Deploy API changes
flyctl deploy --app reflex-chat-api --config fly-fastapi.toml

# Monitor deployment
flyctl logs --app reflex-chat-api --no-tail
flyctl status --app reflex-chat-api
```

**Deploy Reflex Frontend:**
```bash
# Deploy frontend changes
flyctl deploy --app reflex-chat-main

# Monitor deployment
flyctl logs --app reflex-chat-main --no-tail
flyctl status --app reflex-chat-main
```

**Automated Deployment:**
```bash
# Use the production deployment script
./scripts/production_deploy.sh

# This script includes:
# - Pre-deployment health checks
# - Database backup
# - API deployment first
# - Frontend deployment
# - Post-deployment verification
```

### 2. Health Monitoring

**Application Health Checks:**
```bash
# Check all services
flyctl status --app reflex-chat-main
flyctl status --app reflex-chat-api
flyctl status --app reflex-chat-main-db

# Test endpoints
curl -s "https://reflex-chat-main.fly.dev/health"
curl -s "https://reflex-chat-api.fly.dev/"
curl -s "https://reflex-chat-api.fly.dev/api/users/1/user_role_project_summary"
```

**Automated Health Monitoring:**
```bash
# Use the production health monitoring script
./scripts/production_health.sh

# This script checks:
# - All service statuses
# - Endpoint availability
# - Dashboard data functionality
```

### 3. Log Management

**Real-time Monitoring:**
```bash
# Monitor logs in real-time
flyctl logs --app reflex-chat-main
flyctl logs --app reflex-chat-api

# Filter logs
flyctl logs --app reflex-chat-api | grep ERROR
flyctl logs --app reflex-chat-main | grep "dashboard"
```

**Log Analysis:**
```bash
# Get recent logs
flyctl logs --app reflex-chat-main --no-tail | tail -50
flyctl logs --app reflex-chat-api --no-tail | tail -50

# Search for specific issues
flyctl logs --app reflex-chat-api --no-tail | grep -i "error\|exception\|failed"

# Export logs for analysis
flyctl logs --app reflex-chat-api --no-tail > api_logs_$(date +%Y%m%d_%H%M%S).log
```

### 4. Environment Variables & Secrets Management

**Manage Secrets:**
```bash
# List current secrets
flyctl secrets list --app reflex-chat-main
flyctl secrets list --app reflex-chat-api

# Update secrets
flyctl secrets set FASTAPI_URL="https://reflex-chat-api.fly.dev" --app reflex-chat-main
flyctl secrets set DATABASE_URL="postgresql://..." --app reflex-chat-api
flyctl secrets set OPENAI_API_KEY="new_key" --app reflex-chat-api

# Remove secrets
flyctl secrets unset SECRET_NAME --app reflex-chat-main

# Bulk secret update from file
flyctl secrets import --app reflex-chat-main < secrets.env
```

**Secret Management Best Practices:**
```bash
# Create secrets backup
flyctl secrets list --app reflex-chat-main > secrets_backup_main_$(date +%Y%m%d).txt
flyctl secrets list --app reflex-chat-api > secrets_backup_api_$(date +%Y%m%d).txt

# Rotate sensitive secrets regularly
flyctl secrets set CLIENT_SECRET="new_client_secret" --app reflex-chat-main
flyctl secrets set CLIENT_SECRET="new_client_secret" --app reflex-chat-api
```

### 5. Scaling & Performance

**Vertical Scaling:**
```bash
# Scale machine resources
flyctl scale memory 1024 --app reflex-chat-main
flyctl scale memory 2048 --app reflex-chat-api

# Scale CPU
flyctl scale vm shared-cpu-2x --app reflex-chat-api
flyctl scale vm performance-1x --app reflex-chat-main
```

**Horizontal Scaling:**
```bash
# Scale number of instances
flyctl scale count 2 --app reflex-chat-api
flyctl scale count 1 --app reflex-chat-main  # Reflex typically runs single instance

# Auto-scaling configuration
flyctl autoscale set min=1 max=3 --app reflex-chat-api
```

**Performance Monitoring:**
```bash
# Check resource usage
flyctl metrics --app reflex-chat-main
flyctl metrics --app reflex-chat-api
flyctl metrics --app reflex-chat-main-db

# Machine details
flyctl machine list --app reflex-chat-main
flyctl machine list --app reflex-chat-api

# Resource utilization
flyctl machine status [machine-id] --app reflex-chat-api
```

### 6. Rollback Procedures

**Quick Rollback:**
```bash
# List recent releases
flyctl releases --app reflex-chat-main
flyctl releases --app reflex-chat-api

# Rollback to previous version
flyctl releases rollback --app reflex-chat-main
flyctl releases rollback --app reflex-chat-api

# Rollback to specific version
flyctl releases rollback v42 --app reflex-chat-api
```

**Emergency Rollback:**
```bash
# Use the emergency rollback script
./scripts/production_rollback.sh

# This script handles:
# - API rollback first
# - Main app rollback
# - Health verification
```

### 7. Database Operations in Production

**Database Backup & Restore:**
```bash
# Create automated backup with cleanup
./scripts/backup_production_db.sh

# Manual backup
flyctl postgres connect --app reflex-chat-main-db --command "pg_dump reflex_chat_main" > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
flyctl proxy 5433:5432 --app reflex-chat-main-db &
PGPASSWORD="RefLex2024_Prod_DB!" psql -h localhost -p 5433 -U reflex_chat_main -d reflex_chat_main < backup_file.sql
```

**Database Reset & Reseeding:**
```bash
# Safe database reset (with backup)
flyctl postgres connect --app reflex-chat-main-db --command "pg_dump reflex_chat_main > backup_$(date +%Y%m%d_%H%M%S).sql"

# Reset database schema
flyctl ssh console --app reflex-chat-api --command "cd /app && python -c \"
from Reflex_Chat.database.db import reset_database
reset_database()
print('✅ Database reset complete')
\""

# Reseed with fresh data
flyctl ssh console --app reflex-chat-api --command "cd /app && python -c \"
from Reflex_Chat.database.seed_data import seed_all_data
seed_all_data()
print('✅ Database seeding complete')
\""
```

**Database Monitoring:**
```bash
# Performance monitoring
flyctl metrics --app reflex-chat-main-db

# Active connections
flyctl postgres connect --app reflex-chat-main-db --command "
SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';
"

# Database size
flyctl postgres connect --app reflex-chat-main-db --command "
SELECT pg_size_pretty(pg_database_size('reflex_chat_main')) as db_size;
"
```

## 🔧 Daily Operations Workflow

**Daily Health Check:**
```bash
# Run this daily
./scripts/production_health.sh

# Check for errors in logs
flyctl logs --app reflex-chat-api --no-tail | grep -i "error\|warning" | tail -10
flyctl logs --app reflex-chat-main --no-tail | grep -i "error\|warning" | tail -10
```

**Weekly Maintenance:**
```bash
# Weekly backup
./scripts/backup_production_db.sh

# Resource usage review
flyctl metrics --app reflex-chat-main
flyctl metrics --app reflex-chat-api
flyctl metrics --app reflex-chat-main-db

# Security updates check
flyctl releases --app reflex-chat-main | head -5
flyctl releases --app reflex-chat-api | head -5
```

**Deployment Checklist:**
1. ✅ Test changes locally with Docker Compose
2. ✅ Use automated deployment: `./scripts/production_deploy.sh`
   - This includes backup, API deployment, frontend deployment, and verification
3. ✅ Alternative manual steps:
   - Backup: `./scripts/backup_production_db.sh`
   - Deploy API: `flyctl deploy --app reflex-chat-api --config fly-fastapi.toml`
   - Deploy frontend: `flyctl deploy --app reflex-chat-main`
   - Health check: `./scripts/production_health.sh`
4. ✅ Test dashboard functionality in browser
5. ✅ Monitor logs for 10 minutes: `flyctl logs --app reflex-chat-main`

## 🚨 Troubleshooting Guide

**Common Issues & Solutions:**

1. **Application Won't Start:**
   ```bash
   # Check logs for startup errors
   flyctl logs --app reflex-chat-main --no-tail | tail -50
   
   # Check machine status
   flyctl machine list --app reflex-chat-main
   
   # Restart machine if needed
   flyctl machine restart [machine-id] --app reflex-chat-main
   ```

2. **Database Connection Issues:**
   ```bash
   # Verify database is running
   flyctl status --app reflex-chat-main-db
   
   # Check DATABASE_URL secret
   flyctl secrets list --app reflex-chat-api | grep DATABASE_URL
   
   # Test database connection
   flyctl ssh console --app reflex-chat-api --command "cd /app && python -c \"
   from Reflex_Chat.database.db import get_session
   with get_session() as session:
       print('✅ Database connection successful')
   \""
   ```

3. **API Communication Failures:**
   ```bash
   # Check FASTAPI_URL setting
   flyctl secrets list --app reflex-chat-main | grep FASTAPI_URL
   
   # Test API endpoint
   curl -v "https://reflex-chat-api.fly.dev/"
   
   # Check CORS settings
   curl -H "Origin: https://reflex-chat-main.fly.dev" "https://reflex-chat-api.fly.dev/"
   ```

4. **Performance Issues:**
   ```bash
   # Check resource usage
   flyctl metrics --app reflex-chat-main
   flyctl metrics --app reflex-chat-api
   
   # Scale up if needed
   flyctl scale memory 2048 --app reflex-chat-api
   flyctl scale vm performance-1x --app reflex-chat-main
   ```

5. **SSL/HTTPS Issues:**
   ```bash
   # Check certificate status
   flyctl certs list --app reflex-chat-main
   
   # Force HTTPS redirect
   flyctl config set force_https=true --app reflex-chat-main
   ```

## 🛠️ Available Scripts

The following production management scripts are available in the `scripts/` directory:

**Core Operations:**
- `./scripts/production_health.sh` - Comprehensive health monitoring
- `./scripts/production_deploy.sh` - Safe deployment workflow
- `./scripts/production_rollback.sh` - Emergency rollback procedure
- `./scripts/backup_production_db.sh` - Database backup with cleanup

**Database Management:**
- `./scripts/db_connect.sh` - Database connection management for DBeaver
- `./scripts/production_db_setup.sh` - Legacy database operations

**Usage Examples:**
```bash
# Daily health check
./scripts/production_health.sh

# Safe deployment
./scripts/production_deploy.sh

# Emergency rollback
./scripts/production_rollback.sh

# Database backup
./scripts/backup_production_db.sh

# DBeaver connection setup
./scripts/db_connect.sh
```

## 📊 Monitoring & Alerting

**Resource Monitoring:**
```bash
# CPU and Memory usage
flyctl metrics --app reflex-chat-main | grep -E "cpu|memory"
flyctl metrics --app reflex-chat-api | grep -E "cpu|memory"

# Request metrics
flyctl metrics --app reflex-chat-main | grep -E "requests|response"
flyctl metrics --app reflex-chat-api | grep -E "requests|response"

# Database metrics
flyctl metrics --app reflex-chat-main-db | grep -E "connections|queries"
```

**Log Monitoring:**
```bash
# Error monitoring
flyctl logs --app reflex-chat-api --no-tail | grep -i "error" | tail -20
flyctl logs --app reflex-chat-main --no-tail | grep -i "error" | tail -20

# Performance monitoring
flyctl logs --app reflex-chat-api --no-tail | grep -E "slow|timeout|502|503|504" | tail -10

# Authentication monitoring
flyctl logs --app reflex-chat-main --no-tail | grep -E "auth|login|token" | tail -10
```

## 🔐 Security Operations

**Security Monitoring:**
```bash
# Check for failed authentication attempts
flyctl logs --app reflex-chat-main --no-tail | grep -i "failed\|unauthorized\|403"

# Monitor unusual access patterns
flyctl logs --app reflex-chat-api --no-tail | grep -E "POST|PUT|DELETE" | tail -20

# Check SSL certificate status
flyctl certs list --app reflex-chat-main
flyctl certs list --app reflex-chat-api
```

**Secret Rotation:**
```bash
# Rotate OpenAI API key
flyctl secrets set OPENAI_API_KEY="new_key" --app reflex-chat-main
flyctl secrets set OPENAI_API_KEY="new_key" --app reflex-chat-api

# Rotate Azure credentials
flyctl secrets set CLIENT_SECRET="new_secret" --app reflex-chat-main
flyctl secrets set CLIENT_SECRET="new_secret" --app reflex-chat-api

# Verify secret updates
flyctl secrets list --app reflex-chat-main
flyctl secrets list --app reflex-chat-api
```

## 📈 Performance Optimization

**Database Optimization:**
```bash
# Check database performance
flyctl postgres connect --app reflex-chat-main-db --command "
SELECT schemaname,tablename,attname,n_distinct,correlation
FROM pg_stats
WHERE tablename IN ('user', 'evaluation', 'answer')
LIMIT 10;
"

# Analyze query performance
flyctl postgres connect --app reflex-chat-main-db --command "
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;
"
```

**Application Optimization:**
```bash
# Monitor response times
curl -w "@curl-format.txt" -o /dev/null -s "https://reflex-chat-api.fly.dev/api/users/1/user_role_project_summary"

# Check memory usage patterns
flyctl ssh console --app reflex-chat-api --command "free -h && ps aux --sort=-%mem | head -10"

# Monitor disk usage
flyctl ssh console --app reflex-chat-main --command "df -h && du -sh /app/*"
```

## 🔄 Disaster Recovery

**Complete System Recovery:**
```bash
# 1. Verify all services are down
flyctl status --app reflex-chat-main
flyctl status --app reflex-chat-api
flyctl status --app reflex-chat-main-db

# 2. Restart database first
flyctl machine restart [db-machine-id] --app reflex-chat-main-db

# 3. Restart API service
flyctl machine restart [api-machine-id] --app reflex-chat-api

# 4. Restart main application
flyctl machine restart [main-machine-id] --app reflex-chat-main

# 5. Verify recovery
./scripts/production_health.sh
```

**Data Recovery:**
```bash
# Restore from backup
flyctl postgres connect --app reflex-chat-main-db --command "
dropdb reflex_chat_main;
createdb reflex_chat_main;
psql reflex_chat_main < backup_file.sql;
"

# Verify data integrity
flyctl ssh console --app reflex-chat-api --command "cd /app && python -c \"
from Reflex_Chat.database.db import get_session
from Reflex_Chat.database.models import User, Evaluation
with get_session() as session:
    users = session.query(User).count()
    evaluations = session.query(Evaluation).count()
    print(f'Users: {users}, Evaluations: {evaluations}')
\""
```

## 📋 Operations Checklist

**Daily Operations:**
- [ ] Run health check: `./scripts/production_health.sh`
- [ ] Check error logs for issues
- [ ] Monitor resource usage
- [ ] Verify backup completion

**Weekly Operations:**
- [ ] Create database backup: `./scripts/backup_production_db.sh`
- [ ] Review performance metrics
- [ ] Check for security updates
- [ ] Rotate logs if needed

**Monthly Operations:**
- [ ] Review and rotate secrets
- [ ] Analyze performance trends
- [ ] Update dependencies
- [ ] Review scaling needs

**Before Deployment:**
- [ ] Test changes locally
- [ ] Create database backup
- [ ] Review deployment plan
- [ ] Prepare rollback strategy

**After Deployment:**
- [ ] Run health checks
- [ ] Monitor logs for 30 minutes
- [ ] Test critical functionality
- [ ] Document any issues

This comprehensive guide provides all the tools and procedures needed for professional production management of your Fly.io deployment.
