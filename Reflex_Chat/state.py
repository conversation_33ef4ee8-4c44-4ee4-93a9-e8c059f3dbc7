import reflex as rx
import msal
import os
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any
import aiohttp
from openai import OpenAI
import logging
# Database imports will be done lazily at runtime to avoid import-time connections
# from Reflex_Chat.database.crud import get_or_create_user
# from Reflex_Chat.database.models import Project, UserProject, User, Evaluation, EvaluationType
# from sqlmodel import select

from Reflex_Chat.state_types import UserProjectUI, Teammate


# Load environment variables
load_dotenv()



#########################################
###########OPENAI CONFIG#################
#########################################

# Configure API keys and settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "dummy-key-for-build")
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://fastapi:8001")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("azure_auth")

# Check if we're in a build environment (skip validation during Docker build)
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV", "").lower() == "true"

# Only check OpenAI API key if not in build environment
if not IS_BUILD_ENV and not os.getenv("OPENAI_API_KEY"):
    raise Exception("Please set OPENAI_API_KEY environment variable.")


class QA(rx.Base):
    """A question and answer pair."""

    question: str
    answer: str


DEFAULT_CHATS = {
    "Intros": [],
}

#########################################
########AUTHENTICATION CONFIG###########
#########################################

# Azure Authentication Config
CLIENT_ID = os.getenv("CLIENT_ID", "dummy-client-id")
CLIENT_SECRET = os.getenv("CLIENT_SECRET", "dummy-client-secret")
TENANT_ID = os.getenv("TENANT_ID", "dummy-tenant-id")
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
REDIRECT_URI = os.getenv("REDIRECT_URI", "http://localhost:3000/auth/callback")
# Base URL for the application (used for logout redirects)
BASE_URL = os.getenv("BASE_URL", "http://localhost:3000")
LOGIN_REDIRECT = "/"
SCOPES: List[str] = ["User.Read"]

# MSAL App Initialization (only if not in build environment)
if not IS_BUILD_ENV:
    sso_app = msal.ConfidentialClientApplication(
        client_id=CLIENT_ID,
        client_credential=CLIENT_SECRET,
        authority=AUTHORITY,
    )

    # Ensure all required environment variables are loaded
    missing_env_vars = [var for var in ["TENANT_ID", "CLIENT_ID", "CLIENT_SECRET", "REDIRECT_URI"] if not os.getenv(var)]
    if missing_env_vars:
        raise ValueError(f"Missing environment variables: {', '.join(missing_env_vars)}")
else:
    # Create a dummy sso_app for build environment
    sso_app = None

#########################################
###########AUTH STATE CLASS##############
#########################################

class AuthState(rx.State):
    """Global state for managing authentication."""

    _token: Dict[str, str] = {}
    _access_token: str = ""
    _flow: dict = {}  # Stores OAuth flow initiated for login
    auth_uri: str = ""  # Stores Microsoft login URL
    db_user_id: Optional[int] = None  # Stores the database user ID

    user_name: str = ""
    user_role: str = ""

    # Database initialization flag
    db_initialized: bool = False

    @rx.event
    def initialize_database(self):
        """Initialize database at runtime startup."""
        if not self.db_initialized and not IS_BUILD_ENV:
            try:
                print("Initializing database at runtime...")
                from Reflex_Chat.database.db import create_db_and_tables
                create_db_and_tables()
                self.db_initialized = True
                print("Database initialization completed successfully")
            except Exception as e:
                print(f"Warning: Database initialization failed: {e}")
                # Don't fail the app startup if database init fails
        else:
            print("Database already initialized or in build environment")

    def get_current_user_id(self) -> Optional[int]:
        """Get the current user's database ID."""
        return self.db_user_id

    @rx.var(cache=True)
    def check_auth(self) -> bool:
        """Returns True if the user is authenticated."""
        return bool(self._token)

    @rx.var(cache=True)
    def token(self) -> Dict[str, str]:
        """Returns the authentication token (user info)."""
        return {key: str(value) for key, value in self._token.items() if isinstance(value, (str, int, float))}

    @rx.event
    def redirect_sso(self):
        """Redirects user to Microsoft login page."""
        print("[DEBUG] Starting Microsoft login process")

        self.auth_uri = ""

        # Initialize sso_app if not already done (runtime initialization)
        global sso_app
        if sso_app is None:
            try:
                import msal
                sso_app = msal.ConfidentialClientApplication(
                    client_id=os.getenv("CLIENT_ID"),
                    client_credential=os.getenv("CLIENT_SECRET"),
                    authority=f"https://login.microsoftonline.com/{os.getenv('TENANT_ID')}",
                )
            except Exception as e:
                print(f"[ERROR] Failed to initialize MSAL app: {e}")
                self.auth_uri = ""
                return

        try:
            self._flow = sso_app.initiate_auth_code_flow(
                scopes=SCOPES,
                redirect_uri=os.getenv("REDIRECT_URI"),
            )
            self.auth_uri = self._flow["auth_uri"]
            print(f"[DEBUG] Auth URI: {self.auth_uri}")
        except Exception as e:
            print(f"[ERROR] Failed to initiate OAuth flow: {e}")
            self.auth_uri = ""

    def require_auth(self):
        """Redirects to login if user is not authenticated."""
        if not self._token:
            return rx.redirect("/login")

    @rx.event
    def process_auth_code(self):
        """Handles OAuth callback and retrieves user token."""
        print("[DEBUG] Processing authentication callback")

        # Extract authorization code from URL
        query_components = self.router.page.params
        auth_response = {
            "code": query_components.get("code"),
            "state": query_components.get("state"),
        }

        # Ensure valid authentication response
        if not auth_response["code"]:
            print("[ERROR] No authorization code received")
            return rx.redirect("/login?error=no_code")

        # Ensure sso_app is initialized
        global sso_app
        if sso_app is None:
            print("[ERROR] MSAL app not initialized")
            return rx.redirect("/login?error=auth_not_initialized")

        try:
            result = sso_app.acquire_token_by_auth_code_flow(
                self._flow, auth_response, scopes=SCOPES
            )
        except Exception as e:
            print(f"[ERROR] OAuth token exchange failed: {e}")
            return rx.redirect("/login?error=auth_failed")

        if "access_token" not in result:
            print("[ERROR] No access token received")
            return rx.redirect("/login?error=no_token")

        # Store access token and user info in state
        self._access_token = result["access_token"]
        self._token = result.get("id_token_claims", {})
        print("\n=== Azure Authentication Info ===")
        print(f"[DEBUG] Full token claims: {self._token}")
        print(f"[DEBUG] Azure Object ID (oid): {self._token.get('oid', 'Not found')}")
        print(f"[DEBUG] Azure Tenant ID (tid): {self._token.get('tid', 'Not found')}")
        print(f"[DEBUG] User Principal Name (upn): {self._token.get('upn', 'Not found')}")
        print(f"[DEBUG] Preferred Username: {self._token.get('preferred_username', 'Not found')}")
        print("================================\n")

        #Set `token` so Reflex UI updates
        self.token = self._token

        # Save user to database
        try:
            # Extract user info from token
            azure_id = self._token.get("oid", "")  # object ID from Azure
            name = self._token.get("name", "")
            email = self._token.get("preferred_username", "")

            print(f"[DEBUG] Azure user info - ID: {azure_id}, Name: {name}, Email: {email}")

            if azure_id and name and email:
                # Create or update user in database (lazy import)
                try:
                    from Reflex_Chat.database.crud import get_or_create_user
                    user, role_name = get_or_create_user(azure_id=azure_id, name=name, email=email)
                    self.db_user_id = user.id
                    self.user_name = name  # Set user name in state
                    self.user_role = role_name  # Set role in state
                    print(f"[DEBUG] User saved to database with ID: {user.id}")
                    logger.info(f"User saved to database: {user.id}")
                except Exception as db_error:
                    print(f"[ERROR] Database operation failed: {db_error}")
                    # Continue without database - don't fail authentication
            else:
                logger.error(f"Missing user data in token: {self._token}")
        except Exception as e:
            logger.error(f"Failed to save user to database: {e}")
            print(f"[ERROR] Database error: {e}")
            import traceback
            print(f"[ERROR] Full traceback:\n{traceback.format_exc()}")

        logger.info(f"Successfully authenticated as {self._token.get('name', 'Unknown')}")

        return rx.redirect(LOGIN_REDIRECT)

    @rx.event
    def logout(self):
        """Logs out the user."""
        logger.info("Logging out user")
        self._token = {}
        self._access_token = ""
        self.auth_uri = ""
        self.db_user_id = None  # Clear database user ID

        # Log the user out in Microsoft Auth with environment-aware redirect
        return rx.redirect(f"{AUTHORITY}/oauth2/v2.0/logout?post_logout_redirect_uri={BASE_URL}/login")

    @rx.event
    def load_token_from_storage(self):
        """Loads authentication token from localStorage into state."""
        return rx.script("""
        (function() {
            const savedToken = localStorage.getItem('auth_token');
            if (savedToken) {
                const token = JSON.parse(savedToken);
                window._reflex.addEvents([{
                    name: "Reflex_Chat.state.AuthState.load_token_from_storage",
                    payload: { token_data: token }
                }]);
            }
        })();
        """)

    @rx.event
    def load_token_from_storage(self, token_data: Dict):
        """Handles the event from localStorage and sets the token."""
        if token_data:
            print(f"[DEBUG] Loading token into state: {token_data}")
            self._token = token_data

#########################################
###########CHAT STATE CLASS##############
#########################################

class ChatState(AuthState):  # Change this line to inherit from AuthState
    """Handles chat messages and AI responses."""

    # Chat state
    chats: Dict[str, List[QA]] = DEFAULT_CHATS
    current_chat: str = list(DEFAULT_CHATS.keys())[0]
    processing: bool = False
    question: str = ""
    new_chat_name: str = ""

    # Helper method to get OpenAI client
    def get_openai_client(self):
        """Returns an OpenAI client instance."""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise Exception("OPENAI_API_KEY environment variable is required at runtime")
        return OpenAI(api_key=api_key)

    def create_chat(self):
        """Create a new chat."""
        # Add the new chat to the list of chats.
        self.current_chat = self.new_chat_name
        self.chats[self.new_chat_name] = []

    def delete_chat(self):
        """Delete the current chat."""
        del self.chats[self.current_chat]
        if len(self.chats) == 0:
            self.chats = DEFAULT_CHATS
        self.current_chat = list(self.chats.keys())[0]

    def set_chat(self, chat_name: str):
        """Set the name of the current chat.

        Args:
            chat_name: The name of the chat.
        """
        self.current_chat = chat_name

    def set_new_chat_name(self, name: str):
        """Set the name for a new chat.

        Args:
            name: The name of the new chat.
        """
        self.new_chat_name = name

    @rx.var(cache=True)
    def chat_titles(self) -> list[str]:
        """Get the list of chat titles.

        Returns:
            The list of chat names.
        """
        return list(self.chats.keys())

    @rx.event(background=True)
    async def process_question(self, form_data: Dict[str, Any]):
        # Get the question from the form
        question = str(form_data.get("question", ""))

        # Check if the question is empty
        if question == "":
            return

        print("[DEBUG] Processing question:", question)

        # Check if we're already processing this question (to prevent duplicates)
        if self.processing:
            print("[DEBUG] Already processing a question, ignoring this one")
            return

        # Add the question to the list of questions
        async with self:
            qa = QA(question=question, answer="")
            self.chats[self.current_chat].append(qa)
            self.processing = True  # Set processing state immediately within the same lock
        yield

        try:
            # Prepare conversation history
            history = []
            if len(self.chats[self.current_chat]) > 1:
                history = [
                    {"question": qa.question, "answer": qa.answer}
                    for qa in self.chats[self.current_chat][:-1]  # Exclude current question
                ]

            # Use the non-streaming API endpoint
            try:
                print("[DEBUG] Using non-streaming API endpoint")
                # Create a session for the request
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{FASTAPI_URL}/api/rag/question",
                        json={
                            "question": question,
                            "conversation_history": history,
                            "user_name": self.user_name,
                            "user_role": self.user_role,
                            "user_id": self.db_user_id,
                        }
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            raise Exception(f"API error: {response.status} - {error_text}")

                        # Process the response
                        response_data = await response.json()
                        answer = response_data.get("answer", "No answer received")

                        # Update the answer with the full text
                        async with self:
                            self.chats[self.current_chat][-1].answer = answer
                            self.chats = self.chats
                        yield
            except Exception as stream_error:
                print(f"[ERROR] Streaming API failed: {str(stream_error)}")
                print("[DEBUG] Falling back to standard OpenAI")

                # Final fallback to standard OpenAI
                try:
                    model = self.openai_process_question
                    async for value in model(question):
                        yield value
                except Exception as e2:
                    print(f"[ERROR] All fallbacks failed: {str(e2)}")
                    async with self:
                        self.chats[self.current_chat][-1].answer = f"Error processing your question: {str(e2)}"
                        self.chats = self.chats
                    yield
        finally:
            # Reset processing state
            async with self:
                self.processing = False


    # Keep the original OpenAI process for fallback
    async def openai_process_question(self, question: str):
        """Get the response from the API.

        Args:
            form_data: A dict with the current question.
        """

        # Add the question to the list of questions.
        async with self:
            qa = QA(question=question, answer="")
            self.chats[self.current_chat].append(qa)

        # Clear the input and start the processing.
        async with self:
            self.processing = True
        yield

        # Build the messages.
        messages = [
            {
                "role": "system",
                "content": "You are a friendly chatbot named Reflex. Respond in markdown.",
            }
        ]
        for qa in self.chats[self.current_chat]:
            messages.append({"role": "user", "content": qa.question})
            messages.append({"role": "assistant", "content": qa.answer})

        # Remove the last mock answer.
        messages = messages[:-1]

        # Start a new session to answer the question.
        session = self.get_openai_client().chat.completions.create(
            model=os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
            messages=messages,
            stream=True,
        )

        # Stream the results, yielding after every word.
        for item in session:
            if hasattr(item.choices[0].delta, "content"):
                answer_text = item.choices[0].delta.content
                # Ensure answer_text is not None before concatenation
                if answer_text is not None:
                    async with self:
                        self.chats[self.current_chat][-1].answer += answer_text
                        self.chats = self.chats
                else:
                    # Handle the case where answer_text is None, perhaps log it or assign a default value
                    # For example, assigning an empty string if answer_text is None
                    answer_text = ""
                    async with self:
                        self.chats[self.current_chat][-1].answer += answer_text
                        self.chats = self.chats
                yield

        # Toggle the processing flag.
        async with self:
            self.processing = False

#########################################
###########PERFORMANCE EVAL STATE########
#########################################

class PerformanceEvalState(AuthState):
    """State for managing performance evaluation data."""

    user_projects: List[UserProjectUI] = []

    @rx.event(background=True)
    async def load_user_projects(self):
        try:
            # Lazy imports to avoid import-time database connections
            from Reflex_Chat.database.db import get_session
            from Reflex_Chat.database.models import Project, UserProject, User, Evaluation, EvaluationType
            from sqlmodel import select

            user_id = self.get_current_user_id()
            print(f"[DEBUG] Loading projects for user_id: {user_id}")

            if not user_id:
                print("[DEBUG] No user_id found - user may not be authenticated")
                return

            with get_session() as session:
                stmt = select(Project).join(UserProject).where(UserProject.user_id == user_id)
                print(f"[DEBUG] SQL Query: {stmt}")
                projects = session.exec(stmt).all()
                print(f"[DEBUG] Found {len(projects)} projects")

                project_list = []

                for project in projects:
                    print(f"[DEBUG] Processing project: {project.name}")
                    teammates_stmt = select(User).join(UserProject).where(
                        UserProject.project_id == project.id,
                        User.id != user_id
                    )
                    teammates = session.exec(teammates_stmt).all()
                    print(f"[DEBUG] Found {len(teammates)} teammates")

                    # Check for self-evaluation
                    self_eval_stmt = select(Evaluation).where(
                        Evaluation.evaluator_id == user_id,
                        Evaluation.evaluatee_id == user_id,
                        Evaluation.project_id == project.id,
                        Evaluation.evaluation_type == EvaluationType.PERFORMANCE
                    )
                    self_evaluated = bool(session.exec(self_eval_stmt).first())
                    print(f"[DEBUG] Self evaluation status: {self_evaluated}")

                    evaluated_stmt = select(Evaluation.evaluatee_id).where(
                        Evaluation.evaluator_id == user_id,
                        Evaluation.project_id == project.id,
                        Evaluation.evaluation_type == EvaluationType.PERFORMANCE,
                        Evaluation.evaluatee_id != user_id  # Exclude self-evaluation
                    )
                    evaluated_ids = set(session.exec(evaluated_stmt).all())
                    print(f"[DEBUG] Already evaluated {len(evaluated_ids)} teammates")

                    valid_teammates = [
                        Teammate(id=u.id, name=u.name)
                        for u in teammates
                        if u.id not in evaluated_ids
                    ]
                    print(f"[DEBUG] {len(valid_teammates)} valid teammates to evaluate")

                    project_list.append(UserProjectUI(
                        id=project.id,
                        name=project.name,
                        teammates=valid_teammates,
                        self_evaluated=self_evaluated
                    ))

                async with self:
                    self.user_projects = project_list
                    print(f"[DEBUG] Final project list: {len(project_list)} projects")
        except Exception as e:
            print(f"[ERROR] Error loading projects: {e}")
            import traceback
            print(f"[ERROR] Full traceback:\n{traceback.format_exc()}")
            raise

