#!/usr/bin/env python3
"""
Simple HTTP proxy to forward requests from 0.0.0.0:3001 to localhost:3000
This works around Reflex 0.7.3's limitation of binding frontend to localhost only.
"""
import asyncio
import aiohttp
from aiohttp import web
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def proxy_handler(request):
    """Forward all requests to localhost:3000"""
    target_url = f"http://localhost:3000{request.path_qs}"

    try:
        # Create connector that forces HTTP/1.1
        connector = aiohttp.TCPConnector(force_close=True, enable_cleanup_closed=True)
        async with aiohttp.ClientSession(connector=connector) as session:
            # Forward the request
            async with session.request(
                method=request.method,
                url=target_url,
                headers=request.headers,
                data=await request.read() if request.method in ['POST', 'PUT', 'PATCH'] else None
            ) as response:
                # Get response content
                content = await response.read()

                # Filter out problematic headers that can cause HTTP/2 issues
                filtered_headers = {}
                for key, value in response.headers.items():
                    if key.lower() not in ['connection', 'transfer-encoding', 'content-encoding']:
                        filtered_headers[key] = value

                # Create response with same status and filtered headers
                resp = web.Response(
                    body=content,
                    status=response.status,
                    headers=filtered_headers
                )
                return resp

    except Exception as e:
        logger.error(f"Proxy error: {e}")
        return web.Response(text="Proxy Error", status=502)

async def websocket_proxy_handler(request):
    """Handle WebSocket connections"""
    ws = web.WebSocketResponse()
    await ws.prepare(request)
    
    try:
        # Connect to backend WebSocket
        session = aiohttp.ClientSession()
        backend_ws = await session.ws_connect('ws://localhost:3000' + request.path_qs)
        
        # Forward messages between client and backend
        async def forward_to_backend():
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await backend_ws.send_str(msg.data)
                elif msg.type == aiohttp.WSMsgType.BINARY:
                    await backend_ws.send_bytes(msg.data)
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    break
        
        async def forward_to_client():
            async for msg in backend_ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await ws.send_str(msg.data)
                elif msg.type == aiohttp.WSMsgType.BINARY:
                    await ws.send_bytes(msg.data)
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    break
        
        # Run both forwarding tasks
        await asyncio.gather(
            forward_to_backend(),
            forward_to_client(),
            return_exceptions=True
        )
        
    except Exception as e:
        logger.error(f"WebSocket proxy error: {e}")
    finally:
        if 'session' in locals():
            await session.close()
    
    return ws

def create_app():
    app = web.Application()
    # Handle WebSocket connections
    app.router.add_get('/_next/static/chunks/webpack.js', websocket_proxy_handler)
    app.router.add_get('/ws', websocket_proxy_handler)
    # Handle all other requests
    app.router.add_route('*', '/{path:.*}', proxy_handler)
    return app

if __name__ == '__main__':
    app = create_app()
    logger.info("Starting proxy server on 0.0.0.0:3001 -> localhost:3000")
    # Force HTTP/1.1 to avoid HTTP/2 compatibility issues with Fly.io
    web.run_app(app, host='0.0.0.0', port=3001, access_log=logger)
