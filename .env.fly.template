# Template for Fly.io environment variables
# Copy this file and fill in the values, then set them as Fly.io secrets

#AUTHENTICATION CONFIG
TENANT_ID=your_tenant_id
CLIENT_ID=your_client_id
REDIRECT_URI=https://reflex-chat-main.fly.dev/auth/callback
BASE_URL=https://reflex-chat-main.fly.dev
CLIENT_SECRET=your_client_secret

#SEARCH CONFIG
SEARCH_SERVICE_NAME=your_search_service_name
SEARCH_API_KEY=your_search_api_key
BLOB_CONNECTION_STRING=your_blob_connection_string
BLOB_CONTAINER_NAME=your_container_name
INDEX_NAME=your_index_name

#OPENAI CONFIG
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4-turbo

#POSTGRES CONFIG (will be set automatically by Fly.io)
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=postgres

# DATABASE (will be set automatically by Fly.io when attaching database)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# REDIS (will be set automatically by Fly.io when attaching Redis)
REDIS_URL=redis://default:<EMAIL>:6379

# REFLEX PRODUCTION CONFIG
FRONTEND_PORT=3000
BACKEND_PORT=8000
PYTHONUNBUFFERED=1

#API URLS FOR PRODUCTION
API_URL=https://reflex-chat-main.fly.dev
FASTAPI_URL=https://reflex-chat-api.fly.dev
WEBSOCKET_URL=wss://reflex-chat-main.fly.dev

#for seeding data
USER_AZURE_ID=your_user_azure_id
